import { useState, useCallback } from "react";
import { IPdfViewerHookReturn } from "./types";

/**
 * Custom hook để quản lý state của PDF Viewer
 * @returns Object chứa state và functions để control PDF viewer
 */
export const usePdfViewer = (): IPdfViewerHookReturn => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentFileUrl, setCurrentFileUrl] = useState<string | null>(null);
  const [currentFileName, setCurrentFileName] = useState<string | null>(null);

  const openPdfViewer = useCallback((fileUrl: string, fileName?: string) => {
    setCurrentFileUrl(fileUrl);
    setCurrentFileName(fileName || "PDF Document");
    setIsOpen(true);
  }, []);

  const closePdfViewer = useCallback(() => {
    setIsOpen(false);
    // Delay reset để tránh flicker khi modal đóng
    setTimeout(() => {
      setCurrentFileUrl(null);
      setCurrentFileName(null);
    }, 300);
  }, []);

  return {
    isOpen,
    openPdfViewer,
    closePdfViewer,
    currentFileUrl,
    currentFileName,
  };
};
