export interface IWordViewerProps {
  /** URL của file Word cần hiển thị */
  fileUrl: string;
  /** Tên file để hiển thị trong title */
  fileName?: string;
  /** Trạng thái mở/đóng modal */
  open: boolean;
  /** Callback khi đóng modal */
  onClose: () => void;
  /** Width của modal (default: "95vw") */
  width?: string | number;
  /** Height của modal body (default: "85vh") */
  height?: string | number;
  /** Có destroy modal khi close không (default: true) */
  destroyOnClose?: boolean;
  /** Custom style cho container */
  containerStyle?: React.CSSProperties;
  /** Viewer mode: 'office' | 'mammoth' | 'both' (default: 'both') */
  viewerMode?: 'office' | 'mammoth' | 'both';
}

export interface IWordViewerHookProps {
  /** URL của file Word */
  fileUrl?: string;
  /** Tên file */
  fileName?: string;
  /** Viewer mode mặc định */
  defaultViewerMode?: 'office' | 'mammoth' | 'both';
}

export interface IWordViewerHookReturn {
  /** Trạng thái mở/đóng */
  isOpen: boolean;
  /** Function để mở Word viewer */
  openWordViewer: (fileUrl: string, fileName?: string, viewerMode?: 'office' | 'mammoth' | 'both') => void;
  /** Function để đóng Word viewer */
  closeWordViewer: () => void;
  /** URL file hiện tại */
  currentFileUrl: string | null;
  /** Tên file hiện tại */
  currentFileName: string | null;
  /** Viewer mode hiện tại */
  currentViewerMode: 'office' | 'mammoth' | 'both';
}
