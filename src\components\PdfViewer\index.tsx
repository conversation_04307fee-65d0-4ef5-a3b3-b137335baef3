import React from "react";
import {Modal} from "antd";
import {Viewer, Worker} from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/zoom/lib/styles/index.css";
import workerUrl from "pdfjs-dist/build/pdf.worker.js?url";
import {isEqual} from "lodash";
import {memo} from "react";

export interface IPdfViewerProps {
  /** URL của file PDF cần hiển thị */
  fileUrl: string;
  /** Tên file để hiển thị trong title */
  fileName?: string;
  /** Trạng thái mở/đóng modal */
  open: boolean;
  /** Callback khi đóng modal */
  onClose: () => void;
  /** Width của modal (default: "95vw") */
  width?: string | number;
  /** Height của modal body (default: "85vh") */
  height?: string | number;
  /** <PERSON><PERSON> hiển thị zoom plugin không (default: true) */
  enableZoom?: boolean;
  /** <PERSON><PERSON> destroy modal khi close không (default: true) */
  destroyOnClose?: boolean;
  /** Custom style cho container PDF */
  containerStyle?: React.CSSProperties;
}

const PdfViewerComponent: React.FC<IPdfViewerProps> = ({
  fileUrl,
  fileName = "PDF Document",
  open,
  onClose,
  width = "95vw",
  height = "85vh",
  enableZoom = true,
  destroyOnClose = true,
  containerStyle = {},
}) => {
  // Tạo zoom plugin instance với useMemo để tránh recreate
  // const zoomPluginInstance = useMemo(() => zoomPlugin(), []);

  // Tạo plugins array dựa trên enableZoom
  // const plugins = useMemo(() => {
  //   return enableZoom ? [zoomPluginInstance] : [];
  // }, [enableZoom, zoomPluginInstance]);

  // Default container style
  const defaultContainerStyle: React.CSSProperties = {
    height: "100%",
    width: "100%",
    border: "1px solid #d9d9d9",
    borderRadius: "6px",
    ...containerStyle,
  };

  return (
    <Modal
      title={`Xem PDF: ${fileName}`}
      open={open}
      onCancel={onClose}
      width={width}
      style={{top: 10}}
      styles={{
        body: {
          height: height,
          padding: 0,
        },
      }}
      footer={null}
      destroyOnClose={destroyOnClose}
      maskClosable={true}
      keyboard={true}>
      {fileUrl && (
        <div style={defaultContainerStyle}>
          <Worker workerUrl={workerUrl}>
            <Viewer
              fileUrl={fileUrl}
              // plugins={plugins}
            />
          </Worker>
        </div>
      )}
    </Modal>
  );
};

PdfViewerComponent.displayName = "PdfViewer";
export const PdfViewer = memo(PdfViewerComponent, isEqual);

// Export types
// export type {IPdfViewerProps} from "./types";

// Export hook
export {usePdfViewer} from "./usePdfViewer";

// Export presets
export {PdfViewerSmall, PdfViewerMedium, PdfViewerFullscreen, PdfViewerReadOnly, PdfViewerMobile} from "./PdfViewerPresets";

export default PdfViewer;
