import React, { useState } from 'react';
import { Button } from 'antd';
import { WordViewer } from './index';

const DebugWordViewer: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  // Test với một file docx mẫu - thay thế bằng URL thực tế của bạn
  const testFileUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'; // Thay bằng URL .docx thực tế
  
  return (
    <div style={{ padding: '20px' }}>
      <h2>Debug WordViewer - docx-preview</h2>
      
      <Button 
        type="primary" 
        onClick={() => {
          console.log('Opening WordViewer with URL:', testFileUrl);
          setIsOpen(true);
        }}
      >
        Test WordViewer (Both modes)
      </Button>
      
      <WordViewer
        fileUrl={testFileUrl}
        fileName="Test Document"
        open={isOpen}
        onClose={() => {
          console.log('Closing WordViewer');
          setIsOpen(false);
        }}
        viewerMode="both"
        width="90vw"
        height="80vh"
      />
    </div>
  );
};

export default DebugWordViewer;
