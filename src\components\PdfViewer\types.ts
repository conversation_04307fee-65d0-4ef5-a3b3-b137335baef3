export interface IPdfViewerProps {
  /** URL của file PDF cần hiển thị */
  fileUrl: string;
  /** Tên file để hiển thị trong title */
  fileName?: string;
  /** Trạng thái mở/đóng modal */
  open: boolean;
  /** Callback khi đóng modal */
  onClose: () => void;
  /** Width của modal (default: "95vw") */
  width?: string | number;
  /** Height của modal body (default: "85vh") */
  height?: string | number;
  /** Có hiển thị zoom plugin không (default: true) */
  enableZoom?: boolean;
  /** Có destroy modal khi close không (default: true) */
  destroyOnClose?: boolean;
  /** Custom style cho container PDF */
  containerStyle?: React.CSSProperties;
}

export interface IPdfViewerHookProps {
  /** URL của file PDF */
  fileUrl?: string;
  /** Tên file */
  fileName?: string;
}

export interface IPdfViewerHookReturn {
  /** Trạng thái mở/đóng */
  isOpen: boolean;
  /** Function để mở PDF viewer */
  openPdfViewer: (fileUrl: string, fileName?: string) => void;
  /** Function để đóng PDF viewer */
  closePdfViewer: () => void;
  /** URL file hiện tại */
  currentFileUrl: string | null;
  /** Tên file hiện tại */
  currentFileName: string | null;
}
