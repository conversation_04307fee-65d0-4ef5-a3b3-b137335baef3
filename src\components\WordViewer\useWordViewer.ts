import {useState, useCallback} from "react";
import {IWordViewerHookReturn} from "./types";

/**
 * Custom hook để quản lý state của Word Viewer
 * @param defaultViewerMode - Viewer mode mặc định ('office' | 'mammoth' | 'both')
 * @returns Object chứa state và functions để control Word viewer
 */
export const useWordViewer = (defaultViewerMode: "office" | "docx-preview" | "both" = "both"): IWordViewerHookReturn => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentFileUrl, setCurrentFileUrl] = useState<string | null>(null);
  const [currentFileName, setCurrentFileName] = useState<string | null>(null);
  const [currentViewerMode, setCurrentViewerMode] = useState<"office" | "docx-preview" | "both">(defaultViewerMode);

  const openWordViewer = useCallback(
    (fileUrl: string, fileName?: string, viewerMode: "office" | "docx-preview" | "both" = defaultViewerMode) => {
      setCurrentFileUrl(fileUrl);
      setCurrentFileName(fileName || "Word Document");
      setCurrentViewerMode(viewerMode);
      setIsOpen(true);
    },
    [defaultViewerMode],
  );

  const closeWordViewer = useCallback(() => {
    setIsOpen(false);
    // Delay reset để tránh flicker khi modal đóng
    setTimeout(() => {
      setCurrentFileUrl(null);
      setCurrentFileName(null);
      setCurrentViewerMode(defaultViewerMode);
    }, 300);
  }, [defaultViewerMode]);

  return {
    isOpen,
    openWordViewer,
    closeWordViewer,
    currentFileUrl,
    currentFileName,
    currentViewerMode,
  };
};
