import React from "react";
import { PdfViewer } from "./index";
import { IPdfViewerProps } from "./types";

/**
 * PDF Viewer với kích thước nhỏ (modal nhỏ)
 */
export const PdfViewerSmall: React.FC<IPdfViewerProps> = (props) => {
  return (
    <PdfViewer
      {...props}
      width="70vw"
      height="60vh"
    />
  );
};

/**
 * PDF Viewer với kích thước trung bình
 */
export const PdfViewerMedium: React.FC<IPdfViewerProps> = (props) => {
  return (
    <PdfViewer
      {...props}
      width="85vw"
      height="75vh"
    />
  );
};

/**
 * PDF Viewer full screen
 */
export const PdfViewerFullscreen: React.FC<IPdfViewerProps> = (props) => {
  return (
    <PdfViewer
      {...props}
      width="98vw"
      height="90vh"
      containerStyle={{
        border: "none",
        borderRadius: 0,
      }}
    />
  );
};

/**
 * PDF Viewer chỉ xem (không có zoom)
 */
export const PdfViewerReadOnly: React.FC<IPdfViewerProps> = (props) => {
  return (
    <PdfViewer
      {...props}
      enableZoom={false}
    />
  );
};

/**
 * PDF Viewer cho mobile (responsive)
 */
export const PdfViewerMobile: React.FC<IPdfViewerProps> = (props) => {
  return (
    <PdfViewer
      {...props}
      width="100vw"
      height="80vh"
      containerStyle={{
        border: "none",
        borderRadius: 0,
      }}
    />
  );
};
