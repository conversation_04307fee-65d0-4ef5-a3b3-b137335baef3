import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useChiNhanh, useDoiTac, usePhong<PERSON>an, useTinhThanh} from "@src/hooks";
import {useChucDanh} from "@src/hooks/chucDanhStore";
import {useMenuNguoiDung} from "@src/hooks/menuNguoiDungStore";
import {CommonEndpoint} from "@src/services/axios";
import {CommonExecuteResponse} from "@src/services/react-queries";

//LẤY LIST MENU NGƯỜI DÙNG THEO NHÓM
const getListhMenuNguoiDungTheoNhom = async (params: ReactQuery.ILayDanhSachNguoiDungTheoNhomParams): Promise<boolean> => {
  try {
    const response: CommonExecuteResponse = await CommonEndpoint.getCommonExecute({
      ...params,
      actionCode: ACTION_CODE.LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM,
    });
    console.log("getListhMenuNguoiDungTheoNhom response", response);
    if (response.data) {
      useMenuNguoiDung.getState().setMenuNguoiDung(response.data);
      return true;
    }
    return false;
  } catch (error) {
    console.log("getListhMenuNguoiDungTheoNhom error", error);
    return false;
  }
};

//LẤY LIST ĐỐI TÁC
const getListDoiTac = async (): Promise<{data: Array<CommonExecute.Execute.IDoiTac>; tong_so_dong: number}> => {
  try {
    const response: CommonExecuteResponse = await CommonEndpoint.getCommonExecute({
      actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
    });
    if (response.data.length > 0) {
      const dataDoiTac = response.data;
      useDoiTac.getState().setListDoiTac(dataDoiTac);
      return {data: dataDoiTac, tong_so_dong: response.output?.tong_so_dong || 0};
    }
    return {data: [], tong_so_dong: 0};
  } catch (error) {
    console.log("layListDoiTac error", error);
    return {data: [], tong_so_dong: 0};
  }
};

//LẤY LIST CHI NHANH
const getListChiNhanh = async (): Promise<void> => {
  try {
    const response = await CommonEndpoint.getCommonExecute({
      trang: 1,
      so_dong: 1000,
      actionCode: ACTION_CODE.LIET_KE_DANH_SACH_CHI_NHANH,
    });
    const dataChiNhanh = response.data.map(item => ({
      label: item.ma + " - " + item.ten,
      value: item.ma,
      ...item,
    }));
    useChiNhanh.getState().setListChiNhanh(dataChiNhanh);
  } catch (error) {
    console.log("getListChiNhanh", error);
  }
};
//LẤY LIST CHI NHANH
const getDanhMucPhongBan = async (): Promise<void> => {
  try {
    const response = await CommonEndpoint.getCommonExecute({
      trang: 1,
      so_dong: 1000,
      actionCode: ACTION_CODE.LIET_KE_DANH_SACH_PHONG_BAN,
    });
    const dataDanhMucPhongBan = response.data.map((item, idx) => ({
      ...item,
      value: item.ma,
      label: item.ten || `Phòng ${idx + 1}`,
    }));

    usePhongBan.getState().setListPhongBan(dataDanhMucPhongBan);
  } catch (error) {
    console.log("getDanhMucPhongBan", error);
  }
};
//LẤY LIST CHI NHANH
const getDanhMucChucDanh = async (): Promise<void> => {
  try {
    const response = await CommonEndpoint.getCommonExecute({
      trang: 1,
      so_dong: 1000,
      actionCode: ACTION_CODE.GET_DANH_MUC_CHUC_DANH,
    });
    useChucDanh.getState().setListChucDanh(response.data);
  } catch (error) {
    console.log("getDanhMucChucDanh", error);
  }
};

//LẤY LIST TỈNH THÀNH
const getDanhMucTinhThanh = async (): Promise<void> => {
  try {
    const response = await CommonEndpoint.getCommonExecute({
      actionCode: ACTION_CODE.GET_DANH_MUC_TINH_THANH,
    });
    useTinhThanh.getState().setlistTinhThanh(response.data);
  } catch (error) {
    console.log("getDanhMucTinhThanh", error);
  }
};
const getMenuHeThong = async ({nhom = "CLIENT"}: ReactQuery.ILayDanhSachNguoiDungTheoNhomParams): Promise<void> => {
  try {
    const response: CommonExecuteResponse = await CommonEndpoint.getCommonExecute({
      actionCode: ACTION_CODE.LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM,
      nhom: nhom,
    });
    console.log("getMenuHeThong", response);
    useMenuNguoiDung.getState().setMenuNguoiDung(response.data);
  } catch (error) {
    console.log("getMenuHeThong", error);
  }
};
export {getListhMenuNguoiDungTheoNhom, getListDoiTac, getListChiNhanh, getDanhMucPhongBan, getDanhMucChucDanh, getDanhMucTinhThanh, getMenuHeThong};
