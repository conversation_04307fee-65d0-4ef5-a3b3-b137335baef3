.modal-word-viewer .ant-modal-body {
  .ant-tabs-top .ant-tabs-content-holder .ant-tabs-content-top .ant-tabs-tabpane.ant-tabs-tabpane-active {
    height: 100%;
  }
  .ant-tabs-top .ant-tabs-content-holder .ant-tabs-content-top {
    height: 100%;
  }
  .ant-modal-wrap {
    overflow: hidden;
  }
}
.ant-modal-root .ant-modal-wrap {
  overflow: hidden;
}

// Styles for docx-preview
.docx-preview-viewer {
  .docx-preview {
    font-family: "Times New Roman", serif;
    line-height: 1.5;

    // Override default styles if needed
    p {
      margin-bottom: 0.5em;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
    }

    table {
      border-collapse: collapse;
      width: 100%;

      td,
      th {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
    }

    img {
      max-width: 100%;
      height: auto;
    }
  }
}
